import os
import asyncio
import time
import json
from aiohttp import ClientSession, ClientTimeout
from datetime import datetime

# 配置信息（从原文件复制）
GITHUB_TOKEN = "****************************************"
PROXY_URL = "http://127.0.0.1:7897"
DOWNLOAD_DIR = r"E:\GitHub下载库\测试"
DOWNLOAD_TIMEOUT = 30

# 测试用的分支列表（选择几个有代表性的分支）
测试分支列表 = [
    {"name": "10", "sha": "dummy_sha_10"},
    {"name": "20", "sha": "dummy_sha_20"},
    {"name": "100", "sha": "dummy_sha_100"},
    {"name": "main", "sha": "dummy_sha_main"}
]

def sanitize_branch_name(branch_name):
    """规范化分支名称用于文件名"""
    import re
    return re.sub(r'[\\/*?:"<>|]', '_', branch_name)


async def 测试获取文件大小(session, url: str) -> int:
    """测试获取文件大小"""
    try:
        timeout = ClientTimeout(total=10)
        async with session.head(url, ssl=False, proxy=PROXY_URL, timeout=timeout) as response:
            if response.status == 200 and 'content-length' in response.headers:
                return int(response.headers['content-length'])
    except Exception as e:
        print(f"   获取文件大小失败: {str(e)}")
    return 0


async def 测试下载文件(session, branch_name: str, path: str, repo: str, save_path: str) -> bool:
    """测试下载单个文件"""
    print(f"  📥 测试下载: {path}")
    
    # 使用修复后的URL构建方式
    urls = [
        f'https://raw.githubusercontent.com/{repo}/{branch_name}/{path}',
        f'https://cdn.jsdelivr.net/gh/{repo}@{branch_name}/{path}'
    ]
    
    print(f"     URL1: {urls[0]}")
    print(f"     URL2: {urls[1]}")
    
    # 测试获取文件大小
    文件大小 = await 测试获取文件大小(session, urls[0])
    if 文件大小 > 0:
        print(f"     文件大小: {文件大小} 字节")
    
    # 尝试下载
    for i, url in enumerate(urls, 1):
        try:
            print(f"     尝试URL{i}...")
            timeout = ClientTimeout(total=DOWNLOAD_TIMEOUT)
            async with session.get(url, ssl=False, proxy=PROXY_URL, timeout=timeout) as response:
                print(f"     响应状态: {response.status}")
                
                if response.status == 200:
                    content = await response.read()
                    实际大小 = len(content)
                    print(f"     下载成功! 实际大小: {实际大小} 字节")
                    
                    # 保存文件
                    full_path = os.path.join(save_path, path)
                    os.makedirs(os.path.dirname(full_path), exist_ok=True)
                    
                    with open(full_path, 'wb') as f:
                        f.write(content)
                    
                    print(f"     ✅ 文件已保存: {full_path}")
                    return True
                elif response.status == 404:
                    print(f"     ❌ 文件不存在 (404)")
                else:
                    print(f"     ⚠️ HTTP错误: {response.status}")
                    
        except Exception as e:
            print(f"     ❌ 下载异常: {str(e)}")
    
    return False


async def 测试获取分支文件列表(session, repo: str, branch_name: str, sha: str):
    """测试获取分支的文件列表"""
    print(f"\n🔍 获取分支 {branch_name} 的文件列表...")
    
    try:
        headers = {'Authorization': f'Bearer {GITHUB_TOKEN}'}
        tree_url = f"https://api.github.com/repos/{repo}/git/trees/{sha}?recursive=1"
        
        timeout = ClientTimeout(total=30)
        async with session.get(tree_url, headers=headers, ssl=False, proxy=PROXY_URL, timeout=timeout) as response:
            print(f"  API响应状态: {response.status}")
            
            if response.status != 200:
                print(f"  ❌ 获取文件树失败")
                return []
            
            tree_data = await response.json()
            files = [item for item in tree_data.get('tree', []) if item['type'] == 'blob']
            
            print(f"  📁 发现 {len(files)} 个文件")
            
            # 只显示前5个文件
            for i, file_info in enumerate(files[:5]):
                print(f"    {i+1}. {file_info['path']} (SHA: {file_info['sha'][:8]}...)")
            
            if len(files) > 5:
                print(f"    ... 还有 {len(files) - 5} 个文件")
            
            return files
            
    except Exception as e:
        print(f"  ❌ 获取文件列表异常: {str(e)}")
        return []


async def 测试分支下载(session, repo: str, branch_info: dict):
    """测试单个分支的下载"""
    branch_name = branch_info['name']
    sha = branch_info['sha']
    
    print(f"\n{'='*60}")
    print(f"🌀 测试分支: {branch_name}")
    print(f"{'='*60}")
    
    # 首先尝试直接访问分支页面
    branch_url = f"https://github.com/{repo}/tree/{branch_name}"
    print(f"🔗 分支页面: {branch_url}")
    
    # 获取真实的分支SHA
    try:
        headers = {'Authorization': f'Bearer {GITHUB_TOKEN}'}
        branch_api_url = f"https://api.github.com/repos/{repo}/branches/{branch_name}"
        
        timeout = ClientTimeout(total=15)
        async with session.get(branch_api_url, headers=headers, ssl=False, proxy=PROXY_URL, timeout=timeout) as response:
            if response.status == 200:
                branch_data = await response.json()
                真实sha = branch_data['commit']['sha']
                print(f"📋 真实SHA: {真实sha}")
                # 更新SHA
                branch_info['sha'] = 真实sha
            else:
                print(f"⚠️ 无法获取分支信息: HTTP {response.status}")
    except Exception as e:
        print(f"⚠️ 获取分支SHA异常: {str(e)}")
    
    # 获取文件列表
    files = await 测试获取分支文件列表(session, repo, branch_name, branch_info['sha'])
    
    if not files:
        print(f"❌ 分支 {branch_name} 没有文件或获取失败")
        return
    
    # 测试下载前3个文件
    print(f"\n📥 开始测试下载 (最多3个文件)...")
    save_path = os.path.join(DOWNLOAD_DIR, branch_name)
    os.makedirs(save_path, exist_ok=True)
    
    成功计数 = 0
    失败计数 = 0
    
    for i, file_info in enumerate(files[:3]):
        file_path = file_info['path']
        print(f"\n  [{i+1}/3] 测试文件: {file_path}")
        
        if await 测试下载文件(session, branch_name, file_path, repo, save_path):
            成功计数 += 1
        else:
            失败计数 += 1
    
    print(f"\n📊 分支 {branch_name} 测试结果: 成功 {成功计数}, 失败 {失败计数}")


async def main():
    """主测试函数"""
    repo = "SteamAutoCracks/ManifestHub"
    
    print("🧪 GitHub下载修复测试工具")
    print(f"📂 测试下载目录: {DOWNLOAD_DIR}")
    print(f"🎯 测试仓库: {repo}")
    print(f"📋 测试分支数量: {len(测试分支列表)}")
    
    # 确保测试目录存在
    os.makedirs(DOWNLOAD_DIR, exist_ok=True)
    
    开始时间 = time.time()
    
    try:
        async with ClientSession() as session:
            for i, branch_info in enumerate(测试分支列表, 1):
                print(f"\n🔄 进度: [{i}/{len(测试分支列表)}]")
                await 测试分支下载(session, repo, branch_info)
                
                # 分支间延迟
                if i < len(测试分支列表):
                    print(f"\n⏳ 等待3秒后测试下一个分支...")
                    await asyncio.sleep(3)
                    
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        结束时间 = time.time()
        总用时 = 结束时间 - 开始时间
        print(f"\n⏱️ 测试总用时: {总用时:.1f}秒")
        print(f"📁 测试文件保存在: {DOWNLOAD_DIR}")


if __name__ == "__main__":
    print("🕒 测试开始时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    asyncio.run(main())
    print("🕒 测试结束时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
