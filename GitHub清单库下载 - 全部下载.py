import os
import asyncio
import time
import json
import re
import shutil
import traceback
from enum import Enum
from typing import Dict, List, Optional, Tuple
from aiohttp import ClientSession, ClientTimeout
from datetime import datetime

# 配置信息
GITHUB_TOKEN = "****************************************"  # 替换为你的GitHub Token
PROXY_URL = "http://127.0.0.1:7897"  # 替换为你的代理地址
DOWNLOAD_DIR = r"E:\GitHub下载库"  # 替换为你的下载目录
BACKUP_DIR = r"E:\GitHub下载库_备份"  # 新增的备份目录
SKIP_FILES = []  # 需要跳过的文件
BRANCHES_FILE = os.path.join(DOWNLOAD_DIR, "branches.txt")
METADATA_DIR = os.path.join(DOWNLOAD_DIR, '.metadata')  # 元数据存储目录
FAILURE_LOG_DIR = os.path.join(DOWNLOAD_DIR, '.failure_logs')  # 失败日志目录
RETRY_LOG_FILE = os.path.join(FAILURE_LOG_DIR, "重试任务日志.json")  # 重试任务日志文件

# 下载配置
MAX_RETRY_ATTEMPTS = 5  # 单个文件最大重试次数
MAX_RETRY_ROUNDS = 3    # 统一重试最大轮次
DOWNLOAD_TIMEOUT = 60   # 下载超时时间（秒）
LARGE_FILE_TIMEOUT = 180  # 大文件下载超时时间（秒）
LARGE_FILE_SIZE = 10 * 1024 * 1024  # 大文件阈值（10MB）

# 全局状态跟踪
rate_limit_remaining = 5000
rate_limit_reset = int(time.time()) + 3600
total_branches = 0
processed_branches = 0


class 错误类型(Enum):
    """错误类型枚举"""
    网络错误 = "network_error"
    超时错误 = "timeout_error"
    HTTP错误 = "http_error"
    API限制 = "api_limit"
    文件不存在 = "file_not_found"
    权限错误 = "permission_error"
    未知错误 = "unknown_error"


class 失败记录管理器:
    """失败记录管理器"""

    def __init__(self):
        self.失败记录: List[Dict] = []
        self.当前轮次 = 1
        os.makedirs(FAILURE_LOG_DIR, exist_ok=True)

    def 记录失败(self, 分支名: str, 文件路径: str, 错误类型: 错误类型,
                错误信息: str, sha: str = "", 重试次数: int = 0):
        """记录失败信息"""
        失败记录 = {
            "时间戳": datetime.now().isoformat(),
            "分支名": 分支名,
            "文件路径": 文件路径,
            "文件SHA": sha,
            "错误类型": 错误类型.value,
            "错误信息": 错误信息,
            "重试次数": 重试次数,
            "轮次": self.当前轮次
        }
        self.失败记录.append(失败记录)

        # 实时保存到文件
        self._保存失败记录()

    def _保存失败记录(self):
        """保存失败记录到文件"""
        try:
            with open(RETRY_LOG_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.失败记录, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"⚠️ 保存失败记录异常: {str(e)}")

    def 获取失败任务(self, 轮次: Optional[int] = None) -> List[Dict]:
        """获取指定轮次的失败任务"""
        if 轮次 is None:
            轮次 = self.当前轮次
        return [记录 for 记录 in self.失败记录 if 记录["轮次"] == 轮次]

    def 清除成功任务(self, 成功任务: List[Tuple[str, str]]):
        """清除已成功的任务"""
        成功集合 = set(成功任务)
        self.失败记录 = [
            记录 for 记录 in self.失败记录
            if (记录["分支名"], 记录["文件路径"]) not in 成功集合
        ]
        self._保存失败记录()

    def 开始新轮次(self):
        """开始新的重试轮次"""
        self.当前轮次 += 1

    def 获取统计信息(self) -> Dict:
        """获取失败统计信息"""
        统计 = {}
        for 记录 in self.失败记录:
            错误类型 = 记录["错误类型"]
            统计[错误类型] = 统计.get(错误类型, 0) + 1
        return 统计


# 全局失败记录管理器
失败管理器 = 失败记录管理器()


class 性能监控器:
    """性能监控器"""

    def __init__(self):
        self.开始时间 = time.time()
        self.下载统计 = {
            "总文件数": 0,
            "成功文件数": 0,
            "失败文件数": 0,
            "总字节数": 0,
            "平均速度": 0
        }
        self.分支统计 = {
            "总分支数": 0,
            "成功分支数": 0,
            "失败分支数": 0
        }

    def 记录文件下载(self, 成功: bool, 文件大小: int = 0):
        """记录文件下载结果"""
        self.下载统计["总文件数"] += 1
        if 成功:
            self.下载统计["成功文件数"] += 1
            self.下载统计["总字节数"] += 文件大小
        else:
            self.下载统计["失败文件数"] += 1

        # 计算平均速度
        运行时间 = time.time() - self.开始时间
        if 运行时间 > 0:
            self.下载统计["平均速度"] = self.下载统计["总字节数"] / 运行时间

    def 记录分支处理(self, 成功: bool):
        """记录分支处理结果"""
        self.分支统计["总分支数"] += 1
        if 成功:
            self.分支统计["成功分支数"] += 1
        else:
            self.分支统计["失败分支数"] += 1

    def 获取实时统计(self) -> str:
        """获取实时统计信息"""
        运行时间 = time.time() - self.开始时间
        速度_mb = self.下载统计["平均速度"] / (1024 * 1024) if self.下载统计["平均速度"] > 0 else 0

        return (f"📊 实时统计: "
                f"文件 {self.下载统计['成功文件数']}/{self.下载统计['总文件数']} | "
                f"分支 {self.分支统计['成功分支数']}/{self.分支统计['总分支数']} | "
                f"速度 {速度_mb:.2f}MB/s | "
                f"用时 {format_time(int(运行时间))}")


# 全局性能监控器
性能监控 = 性能监控器()


def 分类错误类型(异常: Exception, 响应状态: Optional[int] = None) -> 错误类型:
    """根据异常和响应状态分类错误类型"""
    if 响应状态:
        if 响应状态 == 404:
            return 错误类型.文件不存在
        elif 响应状态 == 403:
            return 错误类型.权限错误
        elif 响应状态 == 429:
            return 错误类型.API限制
        elif 响应状态 >= 400:
            return 错误类型.HTTP错误

    异常类型 = type(异常).__name__
    异常信息 = str(异常).lower()

    if "timeout" in 异常信息 or "timed out" in 异常信息:
        return 错误类型.超时错误
    elif any(网络关键词 in 异常信息 for 网络关键词 in ["connection", "network", "dns", "resolve"]):
        return 错误类型.网络错误
    elif "permission" in 异常信息 or "access" in 异常信息:
        return 错误类型.权限错误
    else:
        return 错误类型.未知错误


def sanitize_branch_name(branch_name):
    """规范化分支名称用于文件名"""
    return re.sub(r'[\\/*?:"<>|]', '_', branch_name)


def save_branches_to_file(branches: list):
    """保存分支列表到文件"""
    try:
        os.makedirs(DOWNLOAD_DIR, exist_ok=True)
        branch_names = [b['name'] for b in branches]
        with open(BRANCHES_FILE, 'w', encoding='utf-8') as f:
            f.write('\n'.join(branch_names))
        print(f"\n✅ 成功保存 {len(branch_names)} 个分支列表到: {BRANCHES_FILE}")
    except Exception as e:
        print(f"\n⚠️ 保存分支列表失败: {str(e)}")


def format_time(seconds):
    """时间格式化"""
    if seconds < 60:
        return f"{seconds}秒"
    minutes, sec = divmod(seconds, 60)
    return f"{minutes}分{sec}秒"


async def handle_rate_limit(response):
    """处理速率限制"""
    global rate_limit_remaining, rate_limit_reset

    if 'X-RateLimit-Remaining' in response.headers:
        remaining = int(response.headers['X-RateLimit-Remaining'])
        reset_timestamp = int(response.headers['X-RateLimit-Reset'])

        rate_limit_remaining = remaining
        rate_limit_reset = reset_timestamp

        if remaining < 100:
            current_time = int(time.time())
            sleep_time = max(reset_timestamp - current_time, 0)
            reset_time = datetime.fromtimestamp(reset_timestamp).strftime("%H:%M:%S")
            print(f"\n⚠️ 剩余请求次数 {remaining} | 将于 {reset_time} 重置 | 等待 {format_time(sleep_time)}")
            await asyncio.sleep(sleep_time + 2)


async def fetch_all_branches(session, repo: str):
    """获取全部分支信息"""
    global total_branches
    branches = []
    page = 1
    headers = {'Authorization': f'Bearer {GITHUB_TOKEN}'}

    print("🔍 开始扫描仓库分支...")
    while True:
        print(f"📖 正在读取第 {page} 页分支...", end="\r")
        url = f"https://api.github.com/repos/{repo}/branches?per_page=100&page={page}"
        try:
            async with session.get(url, headers=headers, ssl=False, proxy=PROXY_URL) as response:
                await handle_rate_limit(response)

                if response.status == 200:
                    data = await response.json()
                    if not data:
                        break
                    branches.extend([{
                        'name': branch['name'],
                        'sha': branch['commit']['sha']
                    } for branch in data])
                    page += 1
                else:
                    print(f"\n⚠️ 获取第 {page} 页失败: HTTP {response.status}")
                    break
        except Exception as e:
            print(f"\n⚠️ 分页获取异常: {str(e)}")
            break

    total_branches = len(branches)
    print(f"\n🎯 共发现 {total_branches} 个分支")
    return branches


async def 获取文件大小(session, url: str) -> int:
    """尝试获取文件大小"""
    try:
        timeout = ClientTimeout(total=10)
        async with session.head(url, ssl=False, proxy=PROXY_URL, timeout=timeout) as response:
            if response.status == 200 and 'content-length' in response.headers:
                return int(response.headers['content-length'])
    except:
        pass
    return 0


def 计算超时时间(文件大小: int, 文件扩展名: str) -> int:
    """根据文件大小和类型计算合适的超时时间"""
    基础超时 = DOWNLOAD_TIMEOUT

    # 根据文件大小调整
    if 文件大小 > LARGE_FILE_SIZE:
        基础超时 = LARGE_FILE_TIMEOUT
    elif 文件大小 > 5 * 1024 * 1024:  # 5MB
        基础超时 = int(DOWNLOAD_TIMEOUT * 1.5)

    # 根据文件类型调整
    大文件扩展名 = {'.zip', '.tar', '.gz', '.7z', '.rar', '.iso', '.dmg', '.exe', '.msi'}
    if 文件扩展名.lower() in 大文件扩展名:
        基础超时 = max(基础超时, LARGE_FILE_TIMEOUT)

    return min(基础超时, 300)  # 最大5分钟


async def download_file(session, sha: str, path: str, repo: str, save_path: str,
                       branch_name: str = "", 是否重试任务: bool = False) -> bool:
    """下载单个文件 - 增强版本"""
    if any(skip_file in path for skip_file in SKIP_FILES):
        print(f"⏩ 跳过文件: {path.ljust(40)}", end="\r")
        return True

    urls = [
        f'https://raw.githubusercontent.com/{repo}/{sha}/{path}',
        f'https://cdn.jsdelivr.net/gh/{repo}@{sha}/{path}'
    ]

    # 智能超时时间计算
    文件扩展名 = os.path.splitext(path)[1]
    预估文件大小 = await 获取文件大小(session, urls[0])
    超时时间 = 计算超时时间(预估文件大小, 文件扩展名)

    最后异常 = None
    最后状态码 = None

    for retry in range(MAX_RETRY_ATTEMPTS, 0, -1):
        for url in urls:
            try:
                状态显示 = f"📥 下载中: {path[:35].ljust(35)} 重试{MAX_RETRY_ATTEMPTS-retry+1}/{MAX_RETRY_ATTEMPTS}"
                if 是否重试任务:
                    状态显示 = f"� 重试下载: {path[:30].ljust(30)} 尝试{MAX_RETRY_ATTEMPTS-retry+1}/{MAX_RETRY_ATTEMPTS}"
                if 预估文件大小 > 0:
                    状态显示 += f" ({预估文件大小//1024}KB)"
                print(状态显示, end="\r")

                timeout = ClientTimeout(total=超时时间)
                async with session.get(url, ssl=False, proxy=PROXY_URL, timeout=timeout) as response:
                    最后状态码 = response.status

                    if response.status == 200:
                        # 获取预期文件大小
                        预期大小 = None
                        if 'content-length' in response.headers:
                            预期大小 = int(response.headers['content-length'])

                        content = await response.read()
                        实际大小 = len(content)

                        # 增强的文件完整性验证
                        if 实际大小 == 0:
                            raise Exception("下载的文件为空")

                        # 验证文件大小一致性
                        if 预期大小 and 实际大小 != 预期大小:
                            raise Exception(f"文件大小不匹配: 预期{预期大小}, 实际{实际大小}")

                        # 检查是否是错误页面（简单检测）
                        if 实际大小 < 100 and b'404' in content.lower():
                            raise Exception("下载到错误页面内容")

                        full_path = os.path.join(save_path, path)
                        os.makedirs(os.path.dirname(full_path), exist_ok=True)

                        # 原子写入文件（先写临时文件再重命名）
                        temp_path = full_path + '.tmp'
                        try:
                            with open(temp_path, 'wb') as f:
                                f.write(content)

                            # 验证临时文件
                            if os.path.exists(temp_path) and os.path.getsize(temp_path) == 实际大小:
                                # 原子重命名
                                if os.path.exists(full_path):
                                    os.remove(full_path)
                                os.rename(temp_path, full_path)
                            else:
                                raise Exception("临时文件写入验证失败")
                        finally:
                            # 清理临时文件
                            if os.path.exists(temp_path):
                                try:
                                    os.remove(temp_path)
                                except:
                                    pass

                        # 最终验证
                        if os.path.exists(full_path) and os.path.getsize(full_path) == 实际大小:
                            成功信息 = f"✅ 下载成功: {path.ljust(40)} ({实际大小} 字节)"
                            if 是否重试任务:
                                成功信息 = f"🎯 重试成功: {path.ljust(40)} ({实际大小} 字节)"
                            print(成功信息)

                            # 记录性能统计
                            性能监控.记录文件下载(True, 实际大小)
                            return True
                        else:
                            raise Exception("最终文件验证失败")

                    elif response.status == 404:
                        # 404错误不需要重试其他URL
                        raise Exception(f"文件不存在 (HTTP {response.status})")
                    else:
                        raise Exception(f"HTTP错误 {response.status}")

            except Exception as e:
                最后异常 = e
                错误信息 = str(e)[:50]
                print(f"⚠️ 下载异常: {path[:30]} - {错误信息}".ljust(80), end="\r")

                # 如果是404错误，不需要尝试其他URL
                if "404" in str(e) or "文件不存在" in str(e):
                    break

                # 智能延迟：根据重试次数和错误类型调整
                延迟时间 = min(2 ** (MAX_RETRY_ATTEMPTS - retry), 10)
                if "timeout" in str(e).lower():
                    延迟时间 *= 2  # 超时错误延迟更久
                await asyncio.sleep(延迟时间)

    # 所有重试都失败，记录失败信息
    错误类型_值 = 分类错误类型(最后异常, 最后状态码)
    失败管理器.记录失败(
        分支名=branch_name,
        文件路径=path,
        错误类型=错误类型_值,
        错误信息=str(最后异常) if 最后异常 else "未知错误",
        sha=sha,
        重试次数=MAX_RETRY_ATTEMPTS
    )

    失败信息 = f"❌ 下载失败: {path.ljust(40)} ({错误类型_值.value})"
    if 是否重试任务:
        失败信息 = f"💥 重试失败: {path.ljust(40)} ({错误类型_值.value})"
    print(失败信息)

    # 记录性能统计
    性能监控.记录文件下载(False, 0)
    return False


async def download_branch(session, repo: str, branch_info: dict, 是否重试任务: bool = False):
    """处理单个分支下载 - 增强版本"""
    global processed_branches
    branch_name = branch_info['name']
    sha = branch_info['sha']
    start_time = time.time()

    状态前缀 = "🔄 重试分支" if 是否重试任务 else "🌀 处理分支"
    print(f"\n{状态前缀}: {branch_name}")

    try:
        # 获取文件树
        headers = {'Authorization': f'Bearer {GITHUB_TOKEN}'}
        tree_url = f"https://api.github.com/repos/{repo}/git/trees/{sha}?recursive=1"

        timeout = ClientTimeout(total=30)
        async with session.get(tree_url, headers=headers, ssl=False, proxy=PROXY_URL, timeout=timeout) as response:
            await handle_rate_limit(response)
            if response.status != 200:
                错误信息 = f"获取文件树失败: HTTP {response.status}"
                print(f"⚠️ {错误信息}")

                # 记录分支级别的失败
                失败管理器.记录失败(
                    分支名=branch_name,
                    文件路径="<分支文件树>",
                    错误类型=分类错误类型(Exception(错误信息), response.status),
                    错误信息=错误信息,
                    sha=sha
                )
                return False

            tree_data = await response.json()
            current_files = {item['path']: item['sha'] for item in tree_data.get('tree', []) if item['type'] == 'blob'}

            # 过滤需要下载的文件
            download_files = {k: v for k, v in current_files.items() if k not in SKIP_FILES}

            if not download_files:
                print(f"📭 分支 {branch_name} 无文件需要下载")
                processed_branches += 1
                return True

            # 检查元数据（仅在非重试任务时进行增量检查）
            需要下载的文件 = download_files
            if not 是否重试任务:
                sanitized_branch = sanitize_branch_name(branch_name)
                metadata_file = os.path.join(METADATA_DIR, f"{sanitized_branch}.json")
                metadata_exists = os.path.exists(metadata_file)

                if metadata_exists:
                    with open(metadata_file, 'r', encoding='utf-8') as f:
                        metadata = json.load(f)
                        previous_files = metadata.get('files', {})
                        previous_download = {k: v for k, v in previous_files.items() if k not in SKIP_FILES}
                else:
                    previous_download = {}

                # 对比文件差异
                if download_files == previous_download:
                    print(f"⏩ 分支 {branch_name} 无变化，跳过下载")
                    processed_branches += 1
                    return True

                # 只下载有变化的文件
                需要下载的文件 = {k: v for k, v in download_files.items() if k not in previous_download or previous_download[k] != v}
                if 需要下载的文件:
                    print(f"📊 分支 {branch_name}: 总文件 {len(download_files)}, 需更新 {len(需要下载的文件)}")

            # 执行下载
            save_path = os.path.join(DOWNLOAD_DIR, branch_name)
            os.makedirs(save_path, exist_ok=True)

            成功计数 = 0
            失败计数 = 0

            # 并发下载文件
            下载任务 = []
            for file_path, file_sha in 需要下载的文件.items():
                task = download_file(session, file_sha, file_path, repo, save_path, branch_name, 是否重试任务)
                下载任务.append(task)

            # 执行所有下载任务
            if 下载任务:
                结果列表 = await asyncio.gather(*下载任务, return_exceptions=True)
                for 结果 in 结果列表:
                    if isinstance(结果, bool) and 结果:
                        成功计数 += 1
                    else:
                        失败计数 += 1

            # 备份更新内容（仅在非重试任务且有成功下载时）
            if not 是否重试任务 and 成功计数 > 0:
                backup_path = os.path.join(BACKUP_DIR, branch_name)
                try:
                    if os.path.exists(backup_path):
                        shutil.rmtree(backup_path)
                    shutil.copytree(save_path, backup_path)
                    print(f"📦 已备份到: {backup_path}")
                except Exception as e:
                    print(f"⚠️ 备份失败: {str(e)}")

                # 保存元数据
                sanitized_branch = sanitize_branch_name(branch_name)
                metadata_file = os.path.join(METADATA_DIR, f"{sanitized_branch}.json")
                os.makedirs(METADATA_DIR, exist_ok=True)
                with open(metadata_file, 'w', encoding='utf-8') as f:
                    json.dump({
                        "branch_sha": sha,
                        "files": current_files,
                        "last_update": datetime.now().isoformat()
                    }, f, indent=2, ensure_ascii=False)

            processed_branches += 1
            time_used = time.time() - start_time

            结果信息 = f"🕒 分支 {branch_name} 完成! 用时 {time_used:.1f}秒"
            if 成功计数 > 0 or 失败计数 > 0:
                结果信息 += f" (成功: {成功计数}, 失败: {失败计数})"
            print(结果信息)

            # 记录分支处理结果
            分支成功 = 失败计数 == 0
            性能监控.记录分支处理(分支成功)
            return 分支成功

    except Exception as e:
        错误类型_值 = 分类错误类型(e)
        失败管理器.记录失败(
            分支名=branch_name,
            文件路径="<分支处理>",
            错误类型=错误类型_值,
            错误信息=str(e),
            sha=sha
        )
        print(f"\n⚠️ 分支 {branch_name} 处理异常: {str(e)}")
        return False


async def 统一重试失败任务(session, repo: str):
    """统一重试所有失败的任务"""
    print(f"\n🔄 开始统一重试失败任务...")

    for 轮次 in range(1, MAX_RETRY_ROUNDS + 1):
        失败管理器.开始新轮次()
        当前轮次失败任务 = 失败管理器.获取失败任务(轮次)

        if not 当前轮次失败任务:
            print(f"🎉 第 {轮次} 轮重试: 没有失败任务需要重试!")
            break

        print(f"\n🔄 第 {轮次} 轮重试开始 - 共 {len(当前轮次失败任务)} 个失败任务")

        # 按分支分组失败任务
        分支任务组 = {}
        for 任务 in 当前轮次失败任务:
            分支名 = 任务["分支名"]
            if 分支名 not in 分支任务组:
                分支任务组[分支名] = []
            分支任务组[分支名].append(任务)

        成功计数 = 0
        失败计数 = 0
        成功任务列表 = []

        # 逐个分支重试
        for 分支索引, (分支名, 分支任务) in enumerate(分支任务组.items(), 1):
            print(f"\n📊 重试进度 [{分支索引}/{len(分支任务组)}] 分支: {分支名} ({len(分支任务)} 个任务)")

            # 处理速率限制
            while rate_limit_remaining < 50:
                wait_time = rate_limit_reset - int(time.time())
                print(f"⏸️ 速率限制等待中... 剩余 {format_time(wait_time)}")
                await asyncio.sleep(5)

            # 为该分支创建重试任务
            分支重试成功 = 0
            分支重试失败 = 0

            for 任务 in 分支任务:
                文件路径 = 任务["文件路径"]
                文件SHA = 任务.get("文件SHA", "")

                # 跳过分支级别的错误
                if 文件路径 in ["<分支文件树>", "<分支处理>"]:
                    continue

                save_path = os.path.join(DOWNLOAD_DIR, 分支名)
                os.makedirs(save_path, exist_ok=True)

                # 重试下载单个文件
                重试结果 = await download_file(
                    session, 文件SHA, 文件路径, repo, save_path, 分支名, True
                )

                if 重试结果:
                    分支重试成功 += 1
                    成功任务列表.append((分支名, 文件路径))
                else:
                    分支重试失败 += 1

            成功计数 += 分支重试成功
            失败计数 += 分支重试失败

            if 分支重试成功 > 0:
                print(f"✅ 分支 {分支名} 重试完成: 成功 {分支重试成功}, 失败 {分支重试失败}")

        # 清除本轮成功的任务
        if 成功任务列表:
            失败管理器.清除成功任务(成功任务列表)

        轮次总结 = f"🔄 第 {轮次} 轮重试完成: 成功 {成功计数}, 失败 {失败计数}"
        if 成功计数 > 0:
            成功率 = (成功计数 / (成功计数 + 失败计数)) * 100
            轮次总结 += f" (成功率: {成功率:.1f}%)"
        print(f"\n{轮次总结}")

        # 如果没有失败任务，提前结束
        if 失败计数 == 0:
            print(f"🎉 所有任务重试成功，提前结束!")
            break

        # 轮次间延迟
        if 轮次 < MAX_RETRY_ROUNDS:
            print(f"⏳ 等待 10 秒后开始下一轮重试...")
            await asyncio.sleep(10)

    # 最终统计
    最终失败任务 = 失败管理器.获取失败任务()
    if 最终失败任务:
        print(f"\n⚠️ 重试完成后仍有 {len(最终失败任务)} 个任务失败")
        统计信息 = 失败管理器.获取统计信息()
        print("📊 失败任务统计:")
        for 错误类型, 数量 in 统计信息.items():
            print(f"   {错误类型}: {数量} 个")
    else:
        print(f"\n🎉 所有任务重试成功!")


async def smart_downloader(session, repo: str, branches: list):
    """智能下载调度 - 增强版本"""
    print("\n🚀 开始主要下载任务")
    success_count = 0
    error_count = 0

    for index, branch_info in enumerate(branches, 1):
        progress = f"[{index}/{total_branches}]"
        实时统计 = 性能监控.获取实时统计()
        print(f"\n📊 总进度 {progress} | 成功 {success_count} | 失败 {error_count}")
        print(f"   {实时统计}")

        # 处理速率限制
        while rate_limit_remaining < 50:
            wait_time = rate_limit_reset - int(time.time())
            print(f"⏸️ 速率限制暂停中... 剩余等待 {format_time(wait_time)} {progress}")
            await asyncio.sleep(5)

        # 执行下载
        if await download_branch(session, repo, branch_info):
            success_count += 1
        else:
            error_count += 1

    print(f"\n🎉 主要下载任务完成! 成功: {success_count}, 失败: {error_count}")

    # 检查是否有失败任务需要重试
    失败任务数量 = len(失败管理器.获取失败任务())
    if 失败任务数量 > 0:
        print(f"\n📋 发现 {失败任务数量} 个失败任务，开始统一重试...")
        await 统一重试失败任务(session, repo)
    else:
        print(f"\n🎉 所有任务都成功完成，无需重试!")


def 生成最终报告():
    """生成最终下载报告"""
    print(f"\n" + "="*80)
    print(f"📊 最终下载报告")
    print(f"="*80)

    # 基本统计
    print(f"📈 基本统计:")
    print(f"   总分支数: {total_branches}")
    print(f"   已处理分支: {processed_branches}")

    # 失败任务统计
    失败任务 = 失败管理器.获取失败任务()
    if 失败任务:
        print(f"\n❌ 失败任务统计:")
        print(f"   总失败任务: {len(失败任务)}")

        # 按错误类型分组
        错误统计 = 失败管理器.获取统计信息()
        for 错误类型, 数量 in 错误统计.items():
            print(f"   {错误类型}: {数量} 个")

        # 按分支分组
        分支失败统计 = {}
        for 任务 in 失败任务:
            分支名 = 任务["分支名"]
            分支失败统计[分支名] = 分支失败统计.get(分支名, 0) + 1

        print(f"\n📋 失败分支详情 (前10个):")
        排序分支 = sorted(分支失败统计.items(), key=lambda x: x[1], reverse=True)[:10]
        for 分支名, 失败数 in 排序分支:
            print(f"   {分支名}: {失败数} 个失败任务")

        print(f"\n💾 详细失败日志已保存到: {RETRY_LOG_FILE}")
    else:
        print(f"\n🎉 所有任务都成功完成!")

    print(f"="*80)


async def main(repo: str):
    """主函数 - 增强版本"""
    开始时间 = time.time()

    print(f"🚀 GitHub清单库下载工具 - 增强版")
    print(f"📂 下载目录: {DOWNLOAD_DIR}")
    print(f"📂 备份目录: {BACKUP_DIR}")
    print(f"📂 失败日志目录: {FAILURE_LOG_DIR}")
    print(f"⚙️ 配置信息:")
    print(f"   单文件最大重试次数: {MAX_RETRY_ATTEMPTS}")
    print(f"   统一重试最大轮次: {MAX_RETRY_ROUNDS}")
    print(f"   下载超时时间: {DOWNLOAD_TIMEOUT}秒")
    print(f"   大文件超时时间: {LARGE_FILE_TIMEOUT}秒")

    # 确保必要目录存在
    for 目录 in [DOWNLOAD_DIR, BACKUP_DIR, METADATA_DIR, FAILURE_LOG_DIR]:
        os.makedirs(目录, exist_ok=True)

    try:
        async with ClientSession() as session:
            # 获取分支列表
            branches = await fetch_all_branches(session, repo)
            if not branches:
                print("❌ 未能获取到分支信息，程序退出")
                return

            # 保存分支列表
            save_branches_to_file(branches)

            # 执行智能下载
            await smart_downloader(session, repo, branches)

    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断程序执行")
    except Exception as e:
        print(f"\n❌ 程序执行异常: {str(e)}")
        traceback.print_exc()
    finally:
        # 生成最终报告
        结束时间 = time.time()
        总用时 = 结束时间 - 开始时间
        print(f"\n⏱️ 总执行时间: {format_time(int(总用时))}")
        生成最终报告()


if __name__ == "__main__":
    repo = "SteamAutoCracks/ManifestHub"
    print("🕒 脚本启动时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    asyncio.run(main(repo))
    print("🕒 脚本结束时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))